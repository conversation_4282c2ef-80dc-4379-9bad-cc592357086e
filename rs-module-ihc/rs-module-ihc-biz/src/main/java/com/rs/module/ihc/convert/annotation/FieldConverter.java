package com.rs.module.ihc.convert.annotation;

/**
 * 字段转换器接口
 * 
 * <AUTHOR>
 */
public interface FieldConverter {
    
    /**
     * 转换字段值
     * 
     * @param value 源字段值
     * @return 转换后的值
     */
    Object convert(Object value);
}

/**
 * 默认字段转换器，直接返回原值
 */
class DefaultFieldConverter implements FieldConverter {
    @Override
    public Object convert(Object value) {
        return value;
    }
}

/**
 * 病史格式化转换器
 */
class MedicalHistoryConverter implements FieldConverter {
    @Override
    public Object convert(Object value) {
        if (value == null || value.toString().trim().isEmpty()) {
            return "无特殊病史";
        }
        return "报病原因：" + value.toString();
    }
}
