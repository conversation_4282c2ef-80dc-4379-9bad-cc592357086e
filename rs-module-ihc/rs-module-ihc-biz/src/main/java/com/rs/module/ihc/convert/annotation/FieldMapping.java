package com.rs.module.ihc.convert.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段映射注解
 * 用于标记字段的转换映射关系
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldMapping {
    
    /**
     * 源字段名
     */
    String source();
    
    /**
     * 目标字段名，默认为当前字段名
     */
    String target() default "";
    
    /**
     * 自定义转换器类
     */
    Class<? extends FieldConverter> converter() default DefaultFieldConverter.class;
    
    /**
     * 是否忽略空值
     */
    boolean ignoreNull() default true;
}
