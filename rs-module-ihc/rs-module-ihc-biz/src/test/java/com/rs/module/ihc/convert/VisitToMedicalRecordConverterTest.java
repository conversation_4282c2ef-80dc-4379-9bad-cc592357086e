package com.rs.module.ihc.convert;

import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Date;

/**
 * VisitToMedicalRecordConverter 测试类
 * 
 * <AUTHOR>
 */
public class VisitToMedicalRecordConverterTest {

    private VisitDO visitDO;

    @BeforeEach
    public void setUp() {
        visitDO = new VisitDO();
        visitDO.setId("visit-001");
        visitDO.setJgrybm("001");
        visitDO.setRyxm("张三");
        visitDO.setMainComplaint("头痛、乏力");
        visitDO.setVisitConclusion("建议休息，多饮水");
        visitDO.setVisitUserid("110101199001011234");
        visitDO.setVisitUserName("李医生");
        visitDO.setVisitTime(new Date());
        visitDO.setDiseaseReason("感冒症状");
    }

    @Test
    public void testBasicConversion() {
        // 使用基础转换器
        MedicalRecordDO medicalRecordDO = VisitToMedicalRecordConverter.INSTANCE.visitToMedicalRecord(visitDO);
        
        // 验证转换结果
        assertNotNull(medicalRecordDO);
        assertEquals(visitDO.getJgrybm(), medicalRecordDO.getJgrybm());
        assertEquals(visitDO.getRyxm(), medicalRecordDO.getJgryxm());
        assertEquals(visitDO.getMainComplaint(), medicalRecordDO.getMainComplaint());
        assertEquals(visitDO.getVisitConclusion(), medicalRecordDO.getDiagnosisResult());
        assertEquals(visitDO.getVisitUserid(), medicalRecordDO.getDoctorIdCard());
        assertEquals(visitDO.getVisitUserName(), medicalRecordDO.getDoctorName());
        assertEquals(visitDO.getVisitTime(), medicalRecordDO.getSignatureTime());
        
        // 验证忽略的字段
        assertNull(medicalRecordDO.getId());
        assertNull(medicalRecordDO.getMedicalHistory());
        assertNull(medicalRecordDO.getPastMedicalHistory());
        
        System.out.println("基础转换测试通过！");
        System.out.println("转换结果：" + medicalRecordDO.toString());
    }

    @Test
    public void testAdvancedConversion() {
        // 使用高级转换器
        MedicalRecordDO medicalRecordDO = AdvancedVisitToMedicalRecordConverter.INSTANCE.visitToMedicalRecordAdvanced(visitDO);
        
        // 验证转换结果
        assertNotNull(medicalRecordDO);
        assertEquals(visitDO.getJgrybm(), medicalRecordDO.getJgrybm());
        assertEquals(visitDO.getRyxm(), medicalRecordDO.getJgryxm());
        assertEquals(visitDO.getMainComplaint(), medicalRecordDO.getMainComplaint());
        assertEquals(visitDO.getVisitConclusion(), medicalRecordDO.getDiagnosisResult());
        assertEquals(visitDO.getVisitUserid(), medicalRecordDO.getDoctorIdCard());
        assertEquals(visitDO.getVisitUserName(), medicalRecordDO.getDoctorName());
        assertEquals(visitDO.getVisitTime(), medicalRecordDO.getSignatureTime());
        
        // 验证自定义映射逻辑
        assertEquals("报病原因：" + visitDO.getDiseaseReason(), medicalRecordDO.getMedicalHistory());
        
        System.out.println("高级转换测试通过！");
        System.out.println("病史格式化结果：" + medicalRecordDO.getMedicalHistory());
    }

    @Test
    public void testNullHandling() {
        // 测试空值处理
        VisitDO nullVisitDO = null;
        MedicalRecordDO result = VisitToMedicalRecordConverter.INSTANCE.visitToMedicalRecord(nullVisitDO);
        assertNull(result);
        
        // 测试部分字段为空的情况
        VisitDO partialVisitDO = new VisitDO();
        partialVisitDO.setJgrybm("002");
        partialVisitDO.setRyxm("李四");
        // 其他字段为空
        
        MedicalRecordDO partialResult = VisitToMedicalRecordConverter.INSTANCE.visitToMedicalRecord(partialVisitDO);
        assertNotNull(partialResult);
        assertEquals("002", partialResult.getJgrybm());
        assertEquals("李四", partialResult.getJgryxm());
        assertNull(partialResult.getMainComplaint());
        
        System.out.println("空值处理测试通过！");
    }

    @Test
    public void testUpdateExistingRecord() {
        // 创建现有的病历记录
        MedicalRecordDO existingRecord = new MedicalRecordDO();
        existingRecord.setId("existing-id");
        existingRecord.setJgrybm("001");
        existingRecord.setJgryxm("张三");
        existingRecord.setPastMedicalHistory("既往有高血压病史");
        
        // 使用巡诊记录更新现有记录
        AdvancedVisitToMedicalRecordConverter.INSTANCE.updateMedicalRecordFromVisit(visitDO, existingRecord);
        
        // 验证更新结果
        assertEquals("existing-id", existingRecord.getId()); // ID 应该保持不变
        assertEquals(visitDO.getJgrybm(), existingRecord.getJgrybm());
        assertEquals(visitDO.getRyxm(), existingRecord.getJgryxm());
        assertEquals(visitDO.getMainComplaint(), existingRecord.getMainComplaint());
        assertEquals(visitDO.getVisitConclusion(), existingRecord.getDiagnosisResult());
        assertEquals("既往有高血压病史", existingRecord.getPastMedicalHistory()); // 应该保持不变
        
        System.out.println("更新现有记录测试通过！");
        System.out.println("更新后的记录：" + existingRecord.toString());
    }
}
