# 通用转换器使用指南

## 概述

为了避免为每个对象转换都创建单独的Converter，我们提供了两种通用的转换解决方案：

1. **配置化通用转换器** (`GenericConverter`) - 通过Map配置字段映射和自定义转换器
2. **注解驱动转换器** (`AnnotationBasedConverter`) - 通过注解定义转换规则

## 方案一：配置化通用转换器

### 基本使用

```java
@Autowired
private GenericConverter genericConverter;

// 定义字段映射
Map<String, String> fieldMapping = new HashMap<>();
fieldMapping.put("ryxm", "jgryxm");                    // 源字段 -> 目标字段
fieldMapping.put("visitConclusion", "diagnosisResult");

// 执行转换
MedicalRecordDO result = genericConverter.convert(visitDO, MedicalRecordDO.class, fieldMapping);
```

### 带自定义转换器

```java
// 自定义转换器
Map<String, Function<Object, Object>> customConverters = new HashMap<>();
customConverters.put("medicalHistory", (diseaseReason) -> {
    if (diseaseReason == null || diseaseReason.toString().trim().isEmpty()) {
        return "无特殊病史";
    }
    return "报病原因：" + diseaseReason.toString();
});

// 执行转换
MedicalRecordDO result = genericConverter.convert(visitDO, MedicalRecordDO.class, 
                                                 fieldMapping, customConverters);
```

### 批量转换

```java
List<MedicalRecordDO> resultList = genericConverter.convertList(
    visitDOList, MedicalRecordDO.class, fieldMapping, customConverters);
```

### 使用服务层

```java
@Autowired
private GenericConvertService genericConvertService;

// 单个转换
CommonResult<MedicalRecordDO> result = genericConvertService.visitToMedicalRecord(
    visitDO, MedicalRecordDO.class);

// 批量转换
CommonResult<List<MedicalRecordDO>> batchResult = genericConvertService.visitListToMedicalRecordList(
    visitDOList, MedicalRecordDO.class);
```

### 便捷方法

```java
// 创建字段映射
Map<String, String> fieldMapping = GenericConvertService.createFieldMapping(
    "ryxm", "jgryxm",
    "visitConclusion", "diagnosisResult",
    "visitUserName", "doctorName"
);

// 创建自定义转换器
Map<String, Function<Object, Object>> customConverters = 
    GenericConvertService.createCustomConverters("medicalHistory", 
            (value) -> "格式化后的病史: " + value);
```

## 方案二：注解驱动转换器

### 定义转换VO

```java
@Data
public class MedicalRecordConvertVO {
    
    @FieldMapping(source = "jgrybm")
    private String jgrybm;

    @FieldMapping(source = "ryxm")
    private String jgryxm;

    @FieldMapping(source = "visitConclusion")
    private String diagnosisResult;

    @FieldMapping(source = "visitUserName")
    private String doctorName;

    @FieldMapping(source = "diseaseReason", converter = MedicalHistoryConverter.class)
    private String medicalHistory;
    
    // 其他字段...
}
```

### 自定义转换器

```java
public class MedicalHistoryConverter implements FieldConverter {
    @Override
    public Object convert(Object value) {
        if (value == null || value.toString().trim().isEmpty()) {
            return "无特殊病史";
        }
        return "报病原因：" + value.toString();
    }
}
```

### 使用注解转换器

```java
@Autowired
private AnnotationBasedConverter annotationBasedConverter;

// 单个转换
MedicalRecordConvertVO result = annotationBasedConverter.convert(visitDO, MedicalRecordConvertVO.class);

// 批量转换
List<MedicalRecordConvertVO> resultList = annotationBasedConverter.convertList(
    visitDOList, MedicalRecordConvertVO.class);
```

## REST API 使用

### 通用转换接口

```bash
# 单个转换
POST /admin/ihc/generic-convert/visit-to-medical-record
Content-Type: application/json

{
    "jgrybm": "001",
    "ryxm": "张三",
    "mainComplaint": "头痛",
    "visitConclusion": "建议休息"
}

# 批量转换
POST /admin/ihc/generic-convert/visit-list-to-medical-record-list
Content-Type: application/json

[
    {
        "jgrybm": "001",
        "ryxm": "张三"
    },
    {
        "jgrybm": "002", 
        "ryxm": "李四"
    }
]
```

## 预定义映射配置

系统启动时会自动注册常用的转换配置：

```java
// VisitDO -> MedicalRecordDO 映射已预定义
MedicalRecordDO result = genericConverter.convert(visitDO, MedicalRecordDO.class, "visitToMedicalRecord");
```

## 扩展新的转换

### 方式1：注册新的映射配置

```java
// 在ConvertMappingConfig中添加
Map<String, String> newMapping = new HashMap<>();
newMapping.put("sourceField", "targetField");
GenericConverter.registerFieldMapping("newConvertType", newMapping);
```

### 方式2：创建新的注解VO

```java
@Data
public class NewConvertVO {
    @FieldMapping(source = "sourceField")
    private String targetField;
    
    @FieldMapping(source = "anotherField", converter = CustomConverter.class)
    private String convertedField;
}
```

## 性能对比

| 转换方式 | 性能 | 易用性 | 灵活性 | 推荐场景 |
|---------|------|--------|--------|----------|
| MapStruct | 最高 | 中等 | 高 | 固定转换规则，高性能要求 |
| 配置化通用转换器 | 中等 | 高 | 高 | 动态转换规则，快速开发 |
| 注解驱动转换器 | 中等 | 最高 | 中等 | 简单转换规则，代码清晰 |
| BeanUtils | 低 | 最高 | 低 | 简单字段拷贝 |

## 最佳实践

1. **简单字段拷贝**：直接使用 `BeanUtils.toBean()`
2. **复杂转换逻辑**：使用配置化通用转换器
3. **固定转换规则**：使用注解驱动转换器
4. **高性能要求**：使用 MapStruct
5. **批量转换**：优先使用通用转换器的批量方法

## 注意事项

1. 字段名必须完全匹配（区分大小写）
2. 自定义转换器会缓存实例，注意线程安全
3. 转换失败时会记录警告日志，不会中断整个转换过程
4. 建议在单元测试中验证转换结果的正确性
